'use client'

import { useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { AuthForm } from '@/components/auth/auth-form'
import { useUser } from '@/hooks/use-user'
import { Droplets } from 'lucide-react'

export default function LoginPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user, loading } = useUser()
  
  const redirectTo = searchParams.get('redirectTo') || '/dashboard'

  useEffect(() => {
    // Redirect if already authenticated
    if (user && !loading) {
      router.push(redirectTo)
    }
  }, [user, loading, router, redirectTo])

  const handleSuccess = () => {
    router.push(redirectTo)
  }

  // Show loading while checking auth state
  if (loading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="bg-blue-gradient p-3 rounded-xl glow-blue mx-auto mb-4 w-fit">
            <Droplets className="h-8 w-8 text-white" />
          </div>
          <p className="text-neutral-400">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if user is already authenticated
  if (user) {
    return null
  }

  return (
    <div className="min-h-screen bg-black flex flex-col">
      {/* Header */}
      <header className="border-b border-neutral-800 bg-neutral-900">
        <div className="w-full px-6 py-4">
          <Link href="/" className="flex items-center space-x-2 w-fit">
            <div className="bg-blue-gradient p-2 rounded-xl glow-blue">
              <Droplets className="h-6 w-6 text-white" />
            </div>
            <span className="text-xl font-semibold text-white">PressureWash Pro</span>
          </Link>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-6 py-12">
        <div className="w-full max-w-md">
          <AuthForm 
            defaultTab="login"
            onSuccess={handleSuccess}
            redirectTo={redirectTo}
          />
          
          <div className="mt-6 text-center">
            <p className="text-neutral-400">
              Don't have an account?{' '}
              <Link 
                href={`/auth/signup${redirectTo !== '/dashboard' ? `?redirectTo=${encodeURIComponent(redirectTo)}` : ''}`}
                className="text-blue-400 hover:text-blue-300 transition-colors"
              >
                Sign up here
              </Link>
            </p>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t border-neutral-800 bg-neutral-900 py-6">
        <div className="text-center text-neutral-500 text-sm">
          <p>&copy; 2024 PressureWash Pro. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}
