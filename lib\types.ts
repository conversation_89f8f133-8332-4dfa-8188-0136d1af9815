// Database types matching the exact schema from the development guide

export interface Profile {
  id: string // UUID, references auth.users.id
  full_name?: string
  avatar_url?: string
  updated_at: string
}

// Authentication types
export interface AuthState {
  user: User | null
  profile: Profile | null
  loading: boolean
  error: string | null
}

export interface AuthError {
  message: string
  code?: string
  details?: any
}

export interface AuthResponse {
  data?: any
  error?: string | null
}

export interface SignUpData {
  email: string
  password: string
  fullName: string
}

export interface SignInData {
  email: string
  password: string
}

export interface User {
  id: string
  email: string
  created_at: string
  updated_at: string
  aud: string
  role: string
  app_metadata: Record<string, any>
  user_metadata: Record<string, any>
}

export interface Session {
  access_token: string
  refresh_token: string
  expires_in: number
  expires_at?: number
  token_type: string
  user: User
}

// Database type for Supabase
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: Profile
        Insert: Omit<Profile, 'id' | 'updated_at'>
        Update: Partial<Omit<Profile, 'id'>>
      }
      businesses: {
        Row: Business
        Insert: Omit<Business, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Business, 'id'>>
      }
      business_members: {
        Row: BusinessMember
        Insert: BusinessMember
        Update: Partial<BusinessMember>
      }
      locations: {
        Row: Location
        Insert: Omit<Location, 'id'>
        Update: Partial<Omit<Location, 'id'>>
      }
      services: {
        Row: Service
        Insert: Omit<Service, 'id'>
        Update: Partial<Omit<Service, 'id'>>
      }
      business_services: {
        Row: BusinessService
        Insert: BusinessService
        Update: Partial<BusinessService>
      }
      portfolio_images: {
        Row: PortfolioImage
        Insert: Omit<PortfolioImage, 'id' | 'uploaded_at'>
        Update: Partial<Omit<PortfolioImage, 'id'>>
      }
      reviews: {
        Row: Review
        Insert: Omit<Review, 'id' | 'created_at'>
        Update: Partial<Omit<Review, 'id'>>
      }
      message_threads: {
        Row: MessageThread
        Insert: Omit<MessageThread, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<MessageThread, 'id'>>
      }
      messages: {
        Row: Message
        Insert: Omit<Message, 'id' | 'sent_at'>
        Update: Partial<Omit<Message, 'id'>>
      }
      leads: {
        Row: Lead
        Insert: Omit<Lead, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<Lead, 'id'>>
      }
      lead_activities: {
        Row: LeadActivity
        Insert: Omit<LeadActivity, 'id' | 'created_at'>
        Update: Partial<Omit<LeadActivity, 'id'>>
      }
      quote_requests: {
        Row: QuoteRequest
        Insert: Omit<QuoteRequest, 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Omit<QuoteRequest, 'id'>>
      }
    }
  }
}

export interface Business {
  id: string // UUID
  owner_id: string // UUID, references profiles.id
  name: string
  slug: string // URL-friendly unique identifier
  description?: string
  phone?: string
  website_url?: string
  avg_rating: number // Denormalized, default 0.0
  review_count: number // Denormalized, default 0
  business_hours?: string[] // Operating hours from Google
  google_place_id?: string // Google Places ID
  google_maps_url?: string // Google Maps URL
  business_status?: string // Operational status
  created_at: string
  updated_at: string
}

export type BusinessRole = 'owner' | 'admin' | 'member'

export interface BusinessMember {
  business_id: string // UUID
  user_id: string // UUID
  role: BusinessRole
  joined_at: string
}

export interface Location {
  id: string // UUID
  business_id: string // UUID, unique
  street_address?: string
  city?: string
  state?: string
  zip_code?: string
  latitude?: number // Decimal(10, 8)
  longitude?: number // Decimal(11, 8)
  // coordinates?: Point // For PostGIS (future enhancement)
}

export interface Service {
  id: number // Serial
  name: string
  description?: string
}

export interface Lead {
  id: string // UUID
  business_id: string // UUID, references businesses.id
  name: string
  email?: string
  phone?: string
  service_type?: string
  property_address?: string
  city?: string
  state?: string
  zip_code?: string
  status: 'new' | 'contacted' | 'quoted' | 'scheduled' | 'completed' | 'lost'
  source: 'website' | 'referral' | 'google' | 'facebook' | 'phone' | 'other'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  estimated_value?: number
  notes?: string
  last_contact_date?: string
  next_follow_up_date?: string
  created_at: string
  updated_at: string
}

export interface LeadActivity {
  id: string // UUID
  lead_id: string // UUID, references leads.id
  activity_type: 'call' | 'email' | 'meeting' | 'quote_sent' | 'follow_up' | 'note'
  description: string
  created_by?: string // UUID, references profiles.id
  created_at: string
}

export interface BusinessService {
  business_id: string // UUID
  service_id: number
  service?: Service
}

export interface PortfolioImage {
  id: string // UUID
  business_id: string // UUID
  image_url: string
  caption?: string
  display_order: number
  uploaded_at: string
}

export interface Review {
  id: string // UUID
  business_id: string // UUID
  author_id?: string // UUID, references profiles.id (nullable for Google reviews)
  author_name?: string // For Google reviews without profiles
  rating: number // 1-5 stars
  title?: string
  content?: string
  review_source?: 'site' | 'google' // Source of the review
  google_review_id?: string // Google review identifier
  created_at: string
  profile?: Profile
}

export interface MessageThread {
  id: string // UUID
  business_id: string // UUID
  user_id: string // UUID, the homeowner
  subject?: string
  status?: 'active' | 'closed' | 'archived'
  created_at: string
  updated_at: string
}

export interface Message {
  id: number // BigSerial
  thread_id: string // UUID
  author_id: string // UUID
  content: string
  attachments?: string[] // Array of file URLs
  read_at?: string // When message was read
  sent_at: string
  author?: Profile // Author profile information
}

export interface MessageThreadWithDetails extends MessageThread {
  business?: Business
  user?: Profile
  messages?: Message[]
}

export interface QuoteRequest {
  id: string // UUID
  business_id: string // UUID, references businesses.id
  customer_name: string
  customer_email: string
  customer_phone: string
  service_address: string
  service_city: string
  service_state: string
  service_zip_code: string
  property_type: 'residential' | 'commercial'
  service_type?: string
  square_footage?: string
  project_description: string
  additional_notes?: string
  preferred_date?: string
  urgency: 'asap' | 'within_week' | 'within_month' | 'flexible'
  status: 'pending' | 'reviewed' | 'quoted' | 'accepted' | 'declined' | 'completed'
  quote_amount?: number
  quote_notes?: string
  business_response?: string
  created_at: string
  updated_at: string
  responded_at?: string
}

// Extended types for UI components
export interface BusinessWithDetails extends Business {
  location?: Location
  services?: Service[]
  portfolio_images?: PortfolioImage[]
  reviews?: ReviewWithProfile[]
  owner?: Profile
  distance?: number // Distance in miles for location-based searches
}

export interface ReviewWithProfile extends Review {
  profile: Profile
}

export interface MessageThreadWithDetails extends MessageThread {
  messages?: Message[]
  business?: Business
  user?: Profile
}

// Legacy types for backward compatibility with existing frontend components
// These will be gradually migrated to the new schema
export interface GalleryImage {
  id: number
  business_id: string
  image_url: string
  caption?: string
  tag?: string
  created_at: string
}
