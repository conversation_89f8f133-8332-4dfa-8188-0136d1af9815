import { Header } from "@/components/header"
import { PricingPlans } from "@/components/pricing/pricing-plans"
import { PricingFAQ } from "@/components/pricing/pricing-faq"
import { PricingTestimonials } from "@/components/pricing/pricing-testimonials"

export default function PricingPage() {
  return (
    <div className="min-h-screen bg-black">
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Find the Right <span className="bg-blue-gradient bg-clip-text text-white">Plan</span> For You
          </h1>
          <p className="text-xl text-neutral-400 mb-8 max-w-3xl mx-auto">
            Choose the perfect plan to grow your pressure washing business and connect with more customers
          </p>
        </div>
      </section>

      {/* Pricing Plans */}
      <PricingPlans />

      {/* Testimonials */}
      <PricingTestimonials />

      {/* FAQ */}
      <PricingFAQ />

      {/* Footer */}
      <footer className="bg-neutral-900 border-t border-neutral-800 py-12 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-white font-semibold mb-4">Pressure Directory</h3>
              <p className="text-neutral-400">The premier directory for pressure washing services.</p>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">For Customers</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <a href="/search" className="hover:text-white transition-colors">
                    Find Services
                  </a>
                </li>
                <li>
                  <a href="/how-it-works" className="hover:text-white transition-colors">
                    How It Works
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">For Businesses</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <a href="/auth/signup" className="hover:text-white transition-colors">
                    List Your Business
                  </a>
                </li>
                <li>
                  <a href="/dashboard" className="hover:text-white transition-colors">
                    Business Dashboard
                  </a>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">Support</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <a href="/contact" className="hover:text-white transition-colors">
                    Contact Us
                  </a>
                </li>
                <li>
                  <a href="/terms" className="hover:text-white transition-colors">
                    Terms of Service
                  </a>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-neutral-800 mt-8 pt-8 text-center text-neutral-400">
            <p>&copy; 2025 PressureWash Pro. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
