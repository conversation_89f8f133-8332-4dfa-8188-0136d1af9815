import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { <PERSON><PERSON><PERSON> } from "next/font/google"
import "./globals.css"
import { UserProvider } from "@/hooks/use-user"
import { Toaster } from "@/components/ui/toaster"
import { ErrorBoundary } from "@/components/error-boundary"

const geist = Geist({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Pressure Directory - Find the Best Pressure Washing Services",
  description:
    "Connect with top-rated pressure washing professionals in your area. Compare services, read reviews, and get quotes instantly.",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${geist.className} bg-black text-white min-h-screen`}>
        <ErrorBoundary>
          <UserProvider>
            {children}
            <Toaster />
          </UserProvider>
        </ErrorBoundary>
      </body>
    </html>
  )
}
