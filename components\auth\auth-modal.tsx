'use client'

import { useState } from 'react'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { AuthForm } from './auth-form'
import { useUser } from '@/hooks/use-user'

interface AuthModalProps {
  children?: React.ReactNode
  defaultTab?: 'login' | 'signup'
  redirectTo?: string
  triggerText?: string
  triggerVariant?: 'default' | 'outline' | 'ghost' | 'link'
  triggerClassName?: string
}

export function AuthModal({ 
  children, 
  defaultTab = 'login', 
  redirectTo,
  triggerText,
  triggerVariant = 'default',
  triggerClassName 
}: AuthModalProps) {
  const [open, setOpen] = useState(false)
  const { user } = useUser()

  // Don't show auth modal if user is already authenticated
  if (user) {
    return null
  }

  const handleSuccess = () => {
    setOpen(false)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button 
            variant={triggerVariant} 
            className={triggerClassName}
          >
            {triggerText || (defaultTab === 'signup' ? 'Sign Up' : 'Sign In')}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md bg-neutral-900 border-neutral-800 p-0">
        <AuthForm 
          onSuccess={handleSuccess}
          defaultTab={defaultTab}
          redirectTo={redirectTo}
        />
      </DialogContent>
    </Dialog>
  )
}

// Convenience components for common use cases
export function LoginModal({ children, ...props }: Omit<AuthModalProps, 'defaultTab'>) {
  return (
    <AuthModal {...props} defaultTab="login">
      {children}
    </AuthModal>
  )
}

export function SignUpModal({ children, ...props }: Omit<AuthModalProps, 'defaultTab'>) {
  return (
    <AuthModal {...props} defaultTab="signup">
      {children}
    </AuthModal>
  )
}
