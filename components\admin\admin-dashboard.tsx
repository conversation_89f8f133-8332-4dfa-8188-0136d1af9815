"use client"

import { Ad<PERSON><PERSON>ead<PERSON> } from "./admin-header"
import { AdminSidebar } from "./admin-sidebar"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import { Users, Building2, Star, Send, TrendingUp, AlertTriangle, Eye, Trash2 } from "lucide-react"

// Mock data for the chart
const signupData = [
  { date: "Jun 20", signups: 12 },
  { date: "Jun 22", signups: 19 },
  { date: "Jun 24", signups: 15 },
  { date: "Jun 26", signups: 27 },
  { date: "Jun 28", signups: 22 },
  { date: "Jun 30", signups: 31 },
  { date: "Jul 02", signups: 28 },
  { date: "Jul 04", signups: 35 },
  { date: "Jul 06", signups: 42 },
  { date: "Jul 08", signups: 38 },
  { date: "Jul 10", signups: 45 },
  { date: "Jul 12", signups: 52 },
  { date: "Jul 14", signups: 48 },
  { date: "Jul 16", signups: 61 },
  { date: "Jul 18", signups: 58 },
]

// Mock reported reviews data
const reportedReviews = [
  {
    id: "1",
    content: "This is spam!",
    business: "AZ Suds Power Washing",
    reporter: "<EMAIL>",
    reason: "Spam",
    date: "2024-07-19",
  },
  {
    id: "2",
    content: "Inappropriate photo in gallery",
    business: "Aqua Clean Services",
    reporter: "<EMAIL>",
    reason: "Inappropriate Content",
    date: "2024-07-18",
  },
  {
    id: "3",
    content: "Fake review - never used this service",
    business: "Phoenix Pressure Pro",
    reporter: "<EMAIL>",
    reason: "Fake Review",
    date: "2024-07-17",
  },
]

export function AdminDashboard() {
  return (
    <div className="min-h-screen bg-black text-white">
      <AdminHeader />

      <div className="flex">
        <AdminSidebar />

        <main className="flex-1 p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-white mb-2">Dashboard Overview</h1>
            <p className="text-neutral-400">Saturday, July 19, 2025</p>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-neutral-400">Total Users</CardTitle>
                <Users className="h-4 w-4 text-blue-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">5,480</div>
                <p className="text-xs text-green-400 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +12% from last month
                </p>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-neutral-400">Total Businesses</CardTitle>
                <Building2 className="h-4 w-4 text-blue-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">412</div>
                <p className="text-xs text-neutral-400">68 Premium subscribers</p>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-neutral-400">Reviews</CardTitle>
                <Star className="h-4 w-4 text-blue-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">1,893</div>
                <p className="text-xs text-green-400 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +8% from last month
                </p>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800 card-hover-blue">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-neutral-400">Leads Sent</CardTitle>
                <Send className="h-4 w-4 text-blue-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-white">972</div>
                <p className="text-xs text-green-400 flex items-center">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +15% from last month
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Signup Chart */}
          <Card className="bg-neutral-900 border-neutral-800 mb-8">
            <CardHeader>
              <CardTitle className="text-white">Recent Signups (Last 30 Days)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={signupData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(0 0% 14.9%)" />
                    <XAxis dataKey="date" stroke="hsl(0 0% 63.9%)" />
                    <YAxis stroke="hsl(0 0% 63.9%)" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: "hsl(0 0% 3.9%)",
                        border: "1px solid hsl(0 0% 14.9%)",
                        borderRadius: "6px",
                        color: "hsl(0 0% 98%)",
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="signups"
                      stroke="hsl(217 91% 60%)"
                      strokeWidth={2}
                      dot={{ fill: "hsl(217 91% 60%)", strokeWidth: 2, r: 4 }}
                      activeDot={{ r: 6, stroke: "hsl(217 91% 60%)", strokeWidth: 2 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Recently Reported Reviews */}
          <Card className="bg-neutral-900 border-neutral-800">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-white flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2 text-yellow-400" />
                Recently Reported Reviews
              </CardTitle>
              <Badge className="bg-yellow-500/10 text-yellow-400 border-yellow-500/20">{reportedReviews.length} Pending</Badge>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportedReviews.map((report) => (
                  <div
                    key={report.id}
                    className="flex items-center justify-between p-4 bg-neutral-800 rounded-lg border border-neutral-700"
                  >
                    <div className="flex-1">
                      <p className="text-white font-medium">"{report.content}"</p>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-neutral-400">
                        <span>On '{report.business}'</span>
                        <span>by {report.reporter}</span>
                        <Badge className="bg-neutral-800 text-neutral-400 border-neutral-700 text-xs">
                          {report.reason}
                        </Badge>
                        <span>{new Date(report.date).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 ml-4">
                      <Button size="sm" variant="outline" className="border-blue-500/20 text-blue-400 bg-transparent hover:bg-blue-500/10 hover:text-blue-300 hover:border-blue-500/40">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      <Button size="sm" className="text-red-400 hover:text-red-300 hover:bg-neutral-800" variant="ghost">
                        <Trash2 className="h-4 w-4 mr-1" />
                        Remove
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}
