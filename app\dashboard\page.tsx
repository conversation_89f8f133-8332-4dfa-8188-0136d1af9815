"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useUser } from "@/hooks/use-user"
import { supabase } from "@/lib/supabase"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { Loader2 } from "lucide-react"
import type { Business } from "@/lib/types"

export default function DashboardPage() {
  const { user, loading } = useUser()
  const router = useRouter()
  const [business, setBusiness] = useState<Business | null>(null)
  const [businessLoading, setBusinessLoading] = useState(true)

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!loading && !user) {
      router.push('/auth/login?redirectTo=/dashboard')
      return
    }

    // Fetch business data when user is available
    if (user) {
      fetchBusiness()
    }
  }, [user, loading, router])

  const fetchBusiness = async () => {
    if (!user) return

    try {
      setBusinessLoading(true)

      if (!supabase) {
        console.log("Supabase not configured, skipping business fetch")
        setBusinessLoading(false)
        return
      }

      // Get business for authenticated user
      const { data, error } = await supabase
        .from("businesses")
        .select("*")
        .eq("owner_id", user.id)
        .limit(1)

      if (error) {
        console.log("Error fetching business:", error)
        setBusiness(null)
      } else if (data && data.length > 0) {
        setBusiness(data[0])
      } else {
        console.log("No business found, will show onboarding")
        setBusiness(null)
      }
    } catch (error) {
      console.log("Business fetch error, will show onboarding:", error)
      setBusiness(null)
    } finally {
      setBusinessLoading(false)
    }
  }

  // Show loading while checking auth or fetching business
  if (loading || businessLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-400" />
          <p className="text-white">
            {loading ? 'Checking authentication...' : 'Loading dashboard...'}
          </p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated (will be redirected)
  if (!user) {
    return null
  }

  return <DashboardLayout initialBusiness={business} />
}
