'use client'

import { useEffect, useState } from 'react'
import { MapView } from './map-view'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { MapPin, Loader2, Search } from 'lucide-react'
import { Button } from './ui/button'
import Link from 'next/link'
import type { BusinessWithDetails } from '@/lib/types'

interface NationalMapProps {
  className?: string
}

export function NationalMap({ className }: NationalMapProps) {
  const [businesses, setBusinesses] = useState<BusinessWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchBusinesses()
  }, [])

  const fetchBusinesses = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch businesses from the search API with a large limit to get all businesses
      const response = await fetch('/api/search?limit=100')
      
      if (!response.ok) {
        throw new Error('Failed to fetch businesses')
      }

      const data = await response.json()
      
      if (data.businesses) {
        setBusinesses(data.businesses)
      } else {
        setError('No businesses found')
      }
    } catch (err) {
      console.error('Error fetching businesses:', err)
      setError('Failed to load businesses')
    } finally {
      setLoading(false)
    }
  }

  // Calculate center of US for national view
  const nationalCenter = { lat: 39.8283, lng: -98.5795 } // Geographic center of US
  const nationalZoom = 4 // Zoom level to show entire US

  if (loading) {
    return (
      <Card className={`bg-neutral-900 border-neutral-800 ${className}`}>
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="bg-blue-500/10 p-2 rounded-lg">
              <MapPin className="h-5 w-5 text-blue-400" />
            </div>
            <div>
              <CardTitle className="text-white">Nationwide Coverage</CardTitle>
              <p className="text-neutral-400 text-sm">Pressure washing services across America</p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center bg-neutral-800 rounded-lg">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-400" />
              <p className="text-neutral-400">Loading nationwide businesses...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={`bg-neutral-900 border-neutral-800 ${className}`}>
        <CardHeader>
          <div className="flex items-center gap-3">
            <div className="bg-red-500/10 p-2 rounded-lg">
              <MapPin className="h-5 w-5 text-red-400" />
            </div>
            <div>
              <CardTitle className="text-white">Nationwide Coverage</CardTitle>
              <p className="text-neutral-400 text-sm">Unable to load map data</p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center bg-neutral-800 rounded-lg">
            <div className="text-center">
              <p className="text-red-400 mb-4">{error}</p>
              <Button 
                onClick={fetchBusinesses}
                variant="outline"
                className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10"
              >
                Try Again
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={`bg-neutral-900 border-neutral-800 ${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="bg-blue-500/10 p-2 rounded-lg">
              <MapPin className="h-5 w-5 text-blue-400" />
            </div>
            <div>
              <CardTitle className="text-white">Nationwide Coverage</CardTitle>
              <p className="text-neutral-400 text-sm">
                {businesses.length} pressure washing services across America
              </p>
            </div>
          </div>
          <Link href="/search">
            <Button 
              variant="outline"
              size="sm"
              className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10 hover:text-blue-300"
            >
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
          </Link>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[400px] rounded-lg overflow-hidden">
          <MapView
            businesses={businesses}
            center={nationalCenter}
            zoom={nationalZoom}
          />
        </div>
        
        {/* Map Stats */}
        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">{businesses.length}</div>
            <div className="text-sm text-neutral-400">Total Businesses</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">
              {new Set(businesses.map(b => b.location?.state).filter(Boolean)).size}
            </div>
            <div className="text-sm text-neutral-400">States Covered</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">
              {new Set(businesses.map(b => b.location?.city).filter(Boolean)).size}
            </div>
            <div className="text-sm text-neutral-400">Cities Served</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">
              {Math.round(businesses.reduce((sum, b) => sum + (b.avg_rating || 0), 0) / businesses.length * 10) / 10}
            </div>
            <div className="text-sm text-neutral-400">Avg Rating</div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-6 text-center">
          <p className="text-neutral-400 mb-4">
            Find pressure washing services in your area
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Link href="/search">
              <Button className="bg-blue-gradient-hover text-white shadow-lg">
                <Search className="h-4 w-4 mr-2" />
                Search by Location
              </Button>
            </Link>
            <Link href="/for-businesses">
              <Button 
                variant="outline"
                className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10"
              >
                List Your Business
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
