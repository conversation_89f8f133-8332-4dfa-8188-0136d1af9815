'use client'

import { useEffect, useState } from 'react'
import { MapView } from './map-view'
import { Loader2 } from 'lucide-react'
import { Button } from './ui/button'
import type { BusinessWithDetails } from '@/lib/types'

interface NationalMapProps {
  className?: string
}

export function NationalMap({ className }: NationalMapProps) {
  const [businesses, setBusinesses] = useState<BusinessWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchBusinesses()
  }, [])

  const fetchBusinesses = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch businesses from the search API with a large limit to get all businesses
      const response = await fetch('/api/search?limit=100')
      
      if (!response.ok) {
        throw new Error('Failed to fetch businesses')
      }

      const data = await response.json()
      
      if (data.businesses) {
        setBusinesses(data.businesses)
      } else {
        setError('No businesses found')
      }
    } catch (err) {
      console.error('Error fetching businesses:', err)
      setError('Failed to load businesses')
    } finally {
      setLoading(false)
    }
  }

  // Calculate center of US for national view
  const nationalCenter = { lat: 39.8283, lng: -98.5795 } // Geographic center of US
  const nationalZoom = 4 // Zoom level to show entire US

  if (loading) {
    return (
      <div className={`h-[400px] flex items-center justify-center bg-neutral-800 rounded-lg ${className}`}>
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-400" />
          <p className="text-neutral-400">Loading map...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`h-[400px] flex items-center justify-center bg-neutral-800 rounded-lg ${className}`}>
        <div className="text-center">
          <p className="text-red-400 mb-4">Unable to load map</p>
          <Button
            onClick={fetchBusinesses}
            variant="outline"
            className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10"
          >
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className={`h-[400px] rounded-lg overflow-hidden ${className}`}>
      <MapView
        businesses={businesses}
        center={nationalCenter}
        zoom={nationalZoom}
      />
    </div>
  )
}
