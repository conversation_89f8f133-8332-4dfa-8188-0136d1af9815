import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Search, Eye, MessageSquare, ArrowRight, CheckCircle, Star, Shield } from "lucide-react"
import Link from "next/link"

export default function HowItWorksPage() {
  return (
    <div className="min-h-screen bg-black">
      <Header />

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <Badge className="bg-blue-500/10 text-blue-400 border-blue-500/20 mb-6">How It Works</Badge>
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Find Your Perfect Pro in <span className="text-white">3 Simple Steps</span>
          </h1>
          <p className="text-xl text-neutral-400 mb-8 max-w-3xl mx-auto">
            Connecting you with trusted, verified pressure washing professionals in your area has never been easier.
          </p>
        </div>
      </section>

      {/* 3-Step Process */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Step 1 */}
            <div className="text-center relative">
              <Card className="bg-neutral-900 border-neutral-800 card-hover-blue mb-6 h-full">
                <CardContent className="p-8 h-full flex flex-col">
                  <div className="bg-blue-gradient w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 glow-blue">
                    <Search className="h-10 w-10 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">Step 1: Search</h3>
                  <p className="text-neutral-400 mb-4 flex-grow">
                    Tell us what you need and where you're located. We'll show you local pressure washing professionals
                    in your area.
                  </p>
                  <ul className="text-sm text-neutral-300 text-left space-y-2">
                    <li>• Enter your zip code or city</li>
                    <li>• Select your service type</li>
                    <li>• View available professionals</li>
                  </ul>
                </CardContent>
              </Card>
              {/* Arrow for desktop */}
              <div className="hidden md:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                <ArrowRight className="h-8 w-8 text-blue-400" />
              </div>
            </div>

            {/* Step 2 */}
            <div className="text-center relative">
              <Card className="bg-neutral-900 border-neutral-800 card-hover-blue mb-6 h-full">
                <CardContent className="p-8 h-full flex flex-col">
                  <div className="bg-blue-gradient w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 glow-blue">
                    <Eye className="h-10 w-10 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">Step 2: Compare</h3>
                  <p className="text-neutral-400 mb-4 flex-grow">
                    Browse professional profiles, view before/after photos, and read authentic reviews from real
                    customers.
                  </p>
                  <ul className="text-sm text-neutral-300 text-left space-y-2">
                    <li>• View detailed business profiles</li>
                    <li>• Check ratings and reviews</li>
                    <li>• Browse photo galleries</li>
                  </ul>
                </CardContent>
              </Card>
              {/* Arrow for desktop */}
              <div className="hidden md:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                <ArrowRight className="h-8 w-8 text-blue-400" />
              </div>
            </div>

            {/* Step 3 */}
            <div className="text-center">
              <Card className="bg-neutral-900 border-neutral-800 card-hover-blue mb-6 h-full">
                <CardContent className="p-8 h-full flex flex-col">
                  <div className="bg-blue-gradient w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6 glow-blue">
                    <MessageSquare className="h-10 w-10 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-4">Step 3: Connect</h3>
                  <p className="text-neutral-400 mb-4 flex-grow">
                    Contact the professional you like best to request a quote. Most respond within 24 hours.
                  </p>
                  <ul className="text-sm text-neutral-300 text-left space-y-2">
                    <li>• Request free quotes</li>
                    <li>• Direct messaging system</li>
                    <li>• Schedule consultations</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-16 px-4 bg-neutral-900/30">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">Why Choose Pressure Directory?</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6 text-center">
                <Shield className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Verified Professionals</h3>
                <p className="text-neutral-400">
                  All businesses are verified with proper licensing and insurance for your peace of mind.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6 text-center">
                <Star className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Real Reviews</h3>
                <p className="text-neutral-400">
                  Read authentic reviews from real customers to make informed decisions about your service provider.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-neutral-900 border-neutral-800">
              <CardContent className="p-6 text-center">
                <CheckCircle className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Quick Response</h3>
                <p className="text-neutral-400">
                  Most professionals respond to quote requests within 24 hours, getting you service fast.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">What You Can Expect</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-2xl font-bold text-white mb-6">Complete Business Profiles</h3>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-blue-400 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="text-white font-semibold">Detailed Service Information</h4>
                    <p className="text-neutral-400">
                      See exactly what services each professional offers, from residential driveways to commercial
                      buildings.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-blue-400 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="text-white font-semibold">Before & After Photos</h4>
                    <p className="text-neutral-400">
                      Browse photo galleries showcasing the quality of work you can expect from each professional.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-blue-400 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="text-white font-semibold">Customer Reviews & Ratings</h4>
                    <p className="text-neutral-400">
                      Read honest feedback from previous customers to help you make the best choice.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-blue-400 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="text-white font-semibold">Direct Contact Information</h4>
                    <p className="text-neutral-400">
                      Get in touch directly with professionals through our secure messaging system or phone.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <Card className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-400">500+</div>
                  <div className="text-sm text-neutral-400">Verified Professionals</div>
                </CardContent>
              </Card>
              <Card className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-400">4.8★</div>
                  <div className="text-sm text-neutral-400">Average Rating</div>
                </CardContent>
              </Card>
              <Card className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-400">24hrs</div>
                  <div className="text-sm text-neutral-400">Average Response</div>
                </CardContent>
              </Card>
              <Card className="bg-neutral-900 border-neutral-800">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-400">100%</div>
                  <div className="text-sm text-neutral-400">Free to Use</div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-neutral-900/50">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">Ready to Get Started?</h2>
          <p className="text-xl text-neutral-400 mb-8 max-w-2xl mx-auto">
            Find a trusted pressure washing professional in your area today. It's completely free to search and get
            quotes.
          </p>
          <Button asChild size="lg" className="bg-blue-gradient-hover text-lg px-8 py-4">
            <Link href="/search">
              Search for Pressure Washers
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
          <p className="text-sm text-neutral-500 mt-4">No account required • 100% free • Get quotes in minutes</p>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-neutral-900 border-t border-neutral-800 py-12 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-white font-semibold mb-4">Pressure Directory</h3>
              <p className="text-neutral-400">The premier directory for pressure washing services.</p>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">For Customers</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <Link href="/search" className="hover:text-white transition-colors">
                    Find Services
                  </Link>
                </li>
                <li>
                  <Link href="/how-it-works" className="hover:text-white transition-colors">
                    How It Works
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">For Businesses</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <Link href="/for-businesses" className="hover:text-white transition-colors">
                    List Your Business
                  </Link>
                </li>
                <li>
                  <Link href="/dashboard" className="hover:text-white transition-colors">
                    Business Dashboard
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-white font-medium mb-4">Support</h4>
              <ul className="space-y-2 text-neutral-400">
                <li>
                  <Link href="/contact" className="hover:text-white transition-colors">
                    Contact Us
                  </Link>
                </li>
                <li>
                  <Link href="/terms" className="hover:text-white transition-colors">
                    Terms of Service
                  </Link>
                </li>
              </ul>
            </div>
          </div>
          <div className="border-t border-neutral-800 mt-8 pt-8 text-center text-neutral-400">
            <p>&copy; 2025 PressureWash Pro. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
