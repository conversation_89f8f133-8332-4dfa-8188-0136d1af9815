# Pressure Directory - Setup Guide

This guide will help you set up the complete pressure washing directory application following the Development Guide specifications.

## Prerequisites

- Node.js 18+ installed
- A Supabase account (free tier is sufficient for development)

## Step 1: Supabase Project Setup

### 1.1 Create a New Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in/up
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - **Name**: `pressure-directory`
   - **Database Password**: Generate a strong password (save this!)
   - **Region**: Choose closest to your location
5. Click "Create new project"
6. Wait for the project to be created (2-3 minutes)

### 1.2 Get Your Project Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (looks like: `https://xxxxx.supabase.co`)
   - **anon public** key (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)
   - **service_role** key (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

### 1.3 Configure Environment Variables

1. In your project root, update `.env.local`:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

## Step 2: Database Schema Setup

### 2.1 Run the Complete Schema Script

1. In your Supabase dashboard, go to **SQL Editor**
2. Click "New Query"
3. Copy the entire contents of `scripts/03-complete-schema.sql`
4. Paste it into the SQL editor
5. Click "Run" to execute the script

This will create:
- All required tables with proper relationships
- Row-Level Security (RLS) policies
- Helper functions for multi-tenancy
- Triggers for denormalized data
- Initial seed data for services

### 2.2 Set up Storage Bucket (for portfolio images)

1. In Supabase dashboard, go to **Storage**
2. Click "Create a new bucket"
3. Name: `business-portfolios`
4. Make it **Public** (check the public checkbox)
5. Click "Create bucket"

### 2.3 Configure Storage Policies

In the SQL Editor, run this query to set up storage policies:

```sql
-- Allow authenticated users to upload images
CREATE POLICY "Authenticated users can upload portfolio images" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'business-portfolios' 
  AND auth.role() = 'authenticated'
);

-- Allow public access to view images
CREATE POLICY "Public can view portfolio images" ON storage.objects
FOR SELECT USING (bucket_id = 'business-portfolios');

-- Allow business owners to delete their own images
CREATE POLICY "Business owners can delete their portfolio images" ON storage.objects
FOR DELETE USING (
  bucket_id = 'business-portfolios' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);
```

## Step 3: Install Dependencies and Run

### 3.1 Install Dependencies

```bash
npm install
# or
pnpm install
```

### 3.2 Run the Development Server

```bash
npm run dev
# or
pnpm dev
```

### 3.3 Open the Application

Navigate to [http://localhost:3000](http://localhost:3000)

## Step 4: Test the Setup

### 4.1 Create a Test Account

1. Go to `/auth/signup`
2. Create a new account with your email
3. Check your email for verification (if required)

### 4.2 Create a Test Business

1. After signing in, you should be redirected to the dashboard
2. Follow the business onboarding flow
3. Fill out your business profile
4. Add some services
5. Upload a portfolio image

### 4.3 Test Public Features

1. Open a new incognito/private browser window
2. Go to your local site
3. Search for businesses in your area
4. View your business profile
5. Test the quote request feature

## Troubleshooting

### Common Issues

1. **"Invalid JWT" errors**: Check that your environment variables are correct
2. **Database connection errors**: Ensure your Supabase project is active
3. **RLS policy errors**: Make sure you ran the complete schema script
4. **Image upload errors**: Verify the storage bucket is set up correctly

### Getting Help

- Check the browser console for detailed error messages
- Review the Supabase logs in your dashboard
- Ensure all environment variables are set correctly

## Next Steps

Once everything is working:

1. **Content Marketing**: Add blog posts using the built-in CMS
2. **SEO Optimization**: Customize meta tags and structured data
3. **Business Seeding**: Add initial business listings to populate the directory
4. **Premium Features**: Implement subscription tiers for businesses
5. **Analytics**: Add tracking to monitor user behavior

## Production Deployment

For production deployment:

1. **Vercel** (recommended): Connect your GitHub repo to Vercel
2. **Environment Variables**: Add all env vars to your hosting platform
3. **Domain Setup**: Configure your custom domain
4. **Database Scaling**: Monitor usage and upgrade Supabase plan if needed

---

This setup follows the exact specifications from the Development Guide, ensuring you have a production-ready pressure washing directory platform.