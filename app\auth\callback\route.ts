import { createServerClient } from '@/lib/supabase'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const next = requestUrl.searchParams.get('next') ?? '/dashboard'

  if (code) {
    const supabase = await createServerClient()
    
    if (supabase) {
      const { error } = await supabase.auth.exchangeCodeForSession(code)
      
      if (!error) {
        // Successful authentication, redirect to intended page
        return NextResponse.redirect(new URL(next, request.url))
      } else {
        console.error('Error exchanging code for session:', error)
        // Redirect to login with error
        return NextResponse.redirect(new URL('/auth/login?error=callback_error', request.url))
      }
    }
  }

  // If no code or supabase client unavailable, redirect to login
  return NextResponse.redirect(new URL('/auth/login', request.url))
}
