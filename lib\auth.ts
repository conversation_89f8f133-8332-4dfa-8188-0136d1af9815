import { createServerClient } from './supabase'
import { redirect } from 'next/navigation'
import type { Profile } from './types'

export async function getUser() {
  const supabase = await createServerClient()
  
  if (!supabase) {
    return null
  }
  
  const { data: { user }, error } = await supabase.auth.getUser()
  
  if (error) {
    console.error('Error getting user:', error)
    return null
  }
  
  return user
}

export async function getSession() {
  const supabase = await createServerClient()
  
  if (!supabase) {
    return null
  }
  
  const { data: { session }, error } = await supabase.auth.getSession()
  
  if (error) {
    console.error('Error getting session:', error)
    return null
  }
  
  return session
}

export async function requireAuth() {
  const user = await getUser()

  if (!user) {
    redirect('/auth/login')
  }

  return user
}

export async function getUserProfile(userId?: string): Promise<Profile | null> {
  const supabase = await createServerClient()
  
  if (!supabase) {
    return null
  }
  
  let targetUserId = userId
  if (!targetUserId) {
    const user = await getUser()
    if (!user) return null
    targetUserId = user.id
  }
  
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', targetUserId)
    .single()
  
  if (error) {
    console.error('Error getting user profile:', error)
    return null
  }
  
  return data
}

export async function createUserProfile(userId: string, profileData: Partial<Profile>) {
  const supabase = await createServerClient()
  
  if (!supabase) {
    throw new Error('Database connection not available')
  }
  
  const { data, error } = await supabase
    .from('profiles')
    .insert({
      id: userId,
      ...profileData,
    })
    .select()
    .single()
  
  if (error) {
    console.error('Error creating user profile:', error)
    throw error
  }
  
  return data
}

export async function updateUserProfile(userId: string, profileData: Partial<Profile>) {
  const supabase = await createServerClient()
  
  if (!supabase) {
    throw new Error('Database connection not available')
  }
  
  const { data, error } = await supabase
    .from('profiles')
    .update({
      ...profileData,
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId)
    .select()
    .single()
  
  if (error) {
    console.error('Error updating user profile:', error)
    throw error
  }
  
  return data
}

export async function signOut() {
  const supabase = await createServerClient()
  
  if (!supabase) {
    redirect('/')
    return
  }
  
  await supabase.auth.signOut()
  redirect('/')
}

export async function refreshSession() {
  const supabase = await createServerClient()
  
  if (!supabase) {
    return null
  }
  
  const { data, error } = await supabase.auth.refreshSession()
  
  if (error) {
    console.error('Error refreshing session:', error)
    return null
  }
  
  return data.session
}