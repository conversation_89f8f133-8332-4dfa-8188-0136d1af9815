/**
 * <PERSON><PERSON>t to geocode all business locations that are missing coordinates
 * This will add latitude and longitude to locations so they show up on the map
 */

import { supabase } from '../lib/supabase'
import { geocodeAddress } from '../lib/geocoding'

interface LocationRecord {
  id: string
  business_id: string
  street_address: string
  city: string
  state: string
  zip_code: string
  latitude: number | null
  longitude: number | null
}

async function geocodeAllLocations() {
  if (!supabase) {
    console.error('Supabase client not available')
    return
  }

  console.log('🗺️  Starting geocoding process...')

  // Get all locations that need geocoding
  const { data: locations, error } = await supabase
    .from('locations')
    .select('*')
    .or('latitude.is.null,longitude.is.null')

  if (error) {
    console.error('Error fetching locations:', error)
    return
  }

  if (!locations || locations.length === 0) {
    console.log('✅ All locations already have coordinates!')
    return
  }

  console.log(`📍 Found ${locations.length} locations that need geocoding`)

  let successCount = 0
  let errorCount = 0

  // Process locations in batches to avoid rate limiting
  const batchSize = 5
  for (let i = 0; i < locations.length; i += batchSize) {
    const batch = locations.slice(i, i + batchSize)
    
    console.log(`\n🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(locations.length / batchSize)}`)

    // Process batch with delay between requests
    for (const location of batch) {
      try {
        const fullAddress = `${location.street_address}, ${location.city}, ${location.state} ${location.zip_code}`
        console.log(`  📍 Geocoding: ${fullAddress}`)

        const result = await geocodeAddress(fullAddress)

        if (result && result.coordinates) {
          // Update the location with coordinates
          const { error: updateError } = await supabase
            .from('locations')
            .update({
              latitude: result.coordinates.lat,
              longitude: result.coordinates.lng
            })
            .eq('id', location.id)

          if (updateError) {
            console.error(`    ❌ Error updating location ${location.id}:`, updateError)
            errorCount++
          } else {
            console.log(`    ✅ Updated: ${result.coordinates.lat}, ${result.coordinates.lng}`)
            successCount++
          }
        } else {
          console.log(`    ⚠️  No coordinates found for: ${fullAddress}`)
          errorCount++
        }

        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 200))

      } catch (error) {
        console.error(`    ❌ Error geocoding location ${location.id}:`, error)
        errorCount++
      }
    }

    // Longer delay between batches
    if (i + batchSize < locations.length) {
      console.log('    ⏳ Waiting before next batch...')
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }

  console.log('\n🎉 Geocoding complete!')
  console.log(`✅ Successfully geocoded: ${successCount} locations`)
  console.log(`❌ Failed to geocode: ${errorCount} locations`)

  // Verify results
  const { data: updatedLocations, error: verifyError } = await supabase
    .from('locations')
    .select('id')
    .not('latitude', 'is', null)
    .not('longitude', 'is', null)

  if (!verifyError && updatedLocations) {
    console.log(`📊 Total locations with coordinates: ${updatedLocations.length}`)
  }
}

// Run the geocoding if this script is executed directly
if (require.main === module) {
  geocodeAllLocations()
    .then(() => {
      console.log('✨ Geocoding script completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Geocoding script failed:', error)
      process.exit(1)
    })
}

export { geocodeAllLocations }
