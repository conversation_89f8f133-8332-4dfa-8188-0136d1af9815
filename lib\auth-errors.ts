// Authentication error handling utilities

export interface AuthErrorDetails {
  code: string
  message: string
  userMessage: string
  action?: string
}

// Map Supabase auth error codes to user-friendly messages
export const AUTH_ERROR_MESSAGES: Record<string, AuthErrorDetails> = {
  // Sign up errors
  'signup_disabled': {
    code: 'signup_disabled',
    message: 'Signup is disabled',
    userMessage: 'Account registration is currently disabled. Please contact support.',
    action: 'contact_support'
  },
  'email_address_invalid': {
    code: 'email_address_invalid',
    message: 'Invalid email address',
    userMessage: 'Please enter a valid email address.',
    action: 'retry'
  },
  'password_too_short': {
    code: 'password_too_short',
    message: 'Password is too short',
    userMessage: 'Password must be at least 6 characters long.',
    action: 'retry'
  },
  'weak_password': {
    code: 'weak_password',
    message: 'Password is too weak',
    userMessage: 'Please choose a stronger password with a mix of letters, numbers, and symbols.',
    action: 'retry'
  },
  'user_already_registered': {
    code: 'user_already_registered',
    message: 'User already registered',
    userMessage: 'An account with this email already exists. Try signing in instead.',
    action: 'redirect_login'
  },

  // Sign in errors
  'invalid_credentials': {
    code: 'invalid_credentials',
    message: 'Invalid login credentials',
    userMessage: 'Invalid email or password. Please check your credentials and try again.',
    action: 'retry'
  },
  'email_not_confirmed': {
    code: 'email_not_confirmed',
    message: 'Email not confirmed',
    userMessage: 'Please check your email and click the confirmation link before signing in.',
    action: 'resend_confirmation'
  },
  'too_many_requests': {
    code: 'too_many_requests',
    message: 'Too many requests',
    userMessage: 'Too many login attempts. Please wait a few minutes before trying again.',
    action: 'wait'
  },
  'user_not_found': {
    code: 'user_not_found',
    message: 'User not found',
    userMessage: 'No account found with this email address. Please check your email or sign up.',
    action: 'redirect_signup'
  },

  // Password reset errors
  'user_not_found_reset': {
    code: 'user_not_found',
    message: 'User not found for password reset',
    userMessage: 'No account found with this email address.',
    action: 'retry'
  },

  // Session errors
  'session_expired': {
    code: 'session_expired',
    message: 'Session has expired',
    userMessage: 'Your session has expired. Please sign in again.',
    action: 'redirect_login'
  },
  'invalid_token': {
    code: 'invalid_token',
    message: 'Invalid or expired token',
    userMessage: 'The authentication token is invalid or has expired. Please sign in again.',
    action: 'redirect_login'
  },

  // Network errors
  'network_error': {
    code: 'network_error',
    message: 'Network connection error',
    userMessage: 'Unable to connect to the server. Please check your internet connection and try again.',
    action: 'retry'
  },
  'server_error': {
    code: 'server_error',
    message: 'Server error',
    userMessage: 'Something went wrong on our end. Please try again in a few moments.',
    action: 'retry'
  },

  // Generic fallback
  'unknown_error': {
    code: 'unknown_error',
    message: 'Unknown error occurred',
    userMessage: 'An unexpected error occurred. Please try again.',
    action: 'retry'
  }
}

// Parse Supabase error and return user-friendly details
export function parseAuthError(error: any): AuthErrorDetails {
  if (!error) {
    return AUTH_ERROR_MESSAGES.unknown_error
  }

  // Handle string errors
  if (typeof error === 'string') {
    // Check for common error patterns in the message
    const lowerError = error.toLowerCase()
    
    if (lowerError.includes('invalid login credentials')) {
      return AUTH_ERROR_MESSAGES.invalid_credentials
    }
    if (lowerError.includes('email not confirmed')) {
      return AUTH_ERROR_MESSAGES.email_not_confirmed
    }
    if (lowerError.includes('user already registered')) {
      return AUTH_ERROR_MESSAGES.user_already_registered
    }
    if (lowerError.includes('too many requests')) {
      return AUTH_ERROR_MESSAGES.too_many_requests
    }
    if (lowerError.includes('password')) {
      return AUTH_ERROR_MESSAGES.weak_password
    }
    if (lowerError.includes('email')) {
      return AUTH_ERROR_MESSAGES.email_address_invalid
    }
    
    // Return generic error with the original message
    return {
      code: 'unknown_error',
      message: error,
      userMessage: error,
      action: 'retry'
    }
  }

  // Handle error objects
  const errorCode = error.code || error.error_code || 'unknown_error'
  const errorMessage = error.message || error.error_description || 'Unknown error'

  // Check if we have a specific mapping for this error code
  if (AUTH_ERROR_MESSAGES[errorCode]) {
    return AUTH_ERROR_MESSAGES[errorCode]
  }

  // Check for common patterns in the error message
  const lowerMessage = errorMessage.toLowerCase()
  
  if (lowerMessage.includes('invalid login credentials')) {
    return AUTH_ERROR_MESSAGES.invalid_credentials
  }
  if (lowerMessage.includes('email not confirmed')) {
    return AUTH_ERROR_MESSAGES.email_not_confirmed
  }
  if (lowerMessage.includes('user already registered')) {
    return AUTH_ERROR_MESSAGES.user_already_registered
  }
  if (lowerMessage.includes('too many requests')) {
    return AUTH_ERROR_MESSAGES.too_many_requests
  }
  if (lowerMessage.includes('network')) {
    return AUTH_ERROR_MESSAGES.network_error
  }

  // Return generic error with the original message
  return {
    code: errorCode,
    message: errorMessage,
    userMessage: errorMessage,
    action: 'retry'
  }
}

// Get user-friendly error message
export function getAuthErrorMessage(error: any): string {
  const errorDetails = parseAuthError(error)
  return errorDetails.userMessage
}

// Check if error suggests a specific action
export function getAuthErrorAction(error: any): string | undefined {
  const errorDetails = parseAuthError(error)
  return errorDetails.action
}

// Logging utility for auth errors
export function logAuthError(context: string, error: any, userId?: string) {
  const errorDetails = parseAuthError(error)
  
  console.error(`Auth Error [${context}]:`, {
    code: errorDetails.code,
    message: errorDetails.message,
    userMessage: errorDetails.userMessage,
    action: errorDetails.action,
    userId,
    timestamp: new Date().toISOString(),
    originalError: error
  })
}
