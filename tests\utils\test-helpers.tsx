/**
 * Test Helper Utilities
 * Common utilities and helpers for testing
 */

import { render, RenderOptions } from '@testing-library/react'
import React, { ReactElement } from 'react'
import { createClient } from '@supabase/supabase-js'
import { getTestEnvironment } from '../config/test-environments'

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialState?: any
}

export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  const { initialState, ...renderOptions } = options

  // Add any providers here (Theme, Auth, etc.)
  function Wrapper({ children }: { children: React.ReactNode }) {
    return <>{children}</>
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Database test helpers
export class DatabaseTestHelper {
  private supabase

  constructor(envName?: string) {
    const env = getTestEnvironment(envName)
    this.supabase = createClient(env.supabaseUrl, env.supabaseServiceKey)
  }

  async createTestUser(userData: {
    email: string
    password: string
    full_name: string
    phone?: string
  }) {
    const { data, error } = await this.supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true,
      user_metadata: {
        full_name: userData.full_name,
        phone: userData.phone,
      },
    })

    if (error) throw error
    return data.user
  }

  async createTestBusiness(businessData: {
    name: string
    slug: string
    description: string
    owner_id: string
    phone?: string
    email?: string
  }) {
    const { data, error } = await this.supabase
      .from('businesses')
      .insert(businessData)
      .select()
      .single()

    if (error) throw error
    return data
  }

  async cleanupTestUser(userId: string) {
    await this.supabase.auth.admin.deleteUser(userId)
  }

  async cleanupTestBusiness(businessId: string) {
    await this.supabase
      .from('businesses')
      .delete()
      .eq('id', businessId)
  }
}

// API test helpers
export class ApiTestHelper {
  private baseUrl: string

  constructor(baseUrl = 'http://localhost:3000') {
    this.baseUrl = baseUrl
  }

  async makeRequest(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const url = `${this.baseUrl}${endpoint}`
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    }

    return fetch(url, { ...defaultOptions, ...options })
  }

  async get(endpoint: string, headers?: Record<string, string>) {
    return this.makeRequest(endpoint, { method: 'GET', headers })
  }

  async post(endpoint: string, body?: any, headers?: Record<string, string>) {
    return this.makeRequest(endpoint, {
      method: 'POST',
      body: body ? JSON.stringify(body) : undefined,
      headers,
    })
  }

  async put(endpoint: string, body?: any, headers?: Record<string, string>) {
    return this.makeRequest(endpoint, {
      method: 'PUT',
      body: body ? JSON.stringify(body) : undefined,
      headers,
    })
  }

  async delete(endpoint: string, headers?: Record<string, string>) {
    return this.makeRequest(endpoint, { method: 'DELETE', headers })
  }

  // Helper to create authorization header
  createAuthHeader(token: string): Record<string, string> {
    return { Authorization: `Bearer ${token}` }
  }
}

// Form testing helpers
export const FormTestHelper = {
  fillInput: async (input: HTMLElement, value: string) => {
    const { fireEvent } = await import('@testing-library/react')
    fireEvent.change(input, { target: { value } })
  },

  submitForm: async (form: HTMLElement) => {
    const { fireEvent } = await import('@testing-library/react')
    fireEvent.submit(form)
  },

  clickButton: async (button: HTMLElement) => {
    const { fireEvent } = await import('@testing-library/react')
    fireEvent.click(button)
  },
}

// Mock data generators
export const MockDataGenerator = {
  user: (overrides: Partial<any> = {}) => ({
    id: '550e8400-e29b-41d4-a716-446655440000',
    email: '<EMAIL>',
    full_name: 'Test User',
    phone: '******-0100',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides,
  }),

  business: (overrides: Partial<any> = {}) => ({
    id: '660e8400-e29b-41d4-a716-446655440000',
    name: 'Test Business',
    slug: 'test-business',
    description: 'A test business for testing purposes',
    phone: '******-0200',
    email: '<EMAIL>',
    status: 'active',
    subscription_tier: 'free',
    avg_rating: 4.5,
    review_count: 10,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides,
  }),

  review: (overrides: Partial<any> = {}) => ({
    id: 'bb0e8400-e29b-41d4-a716-446655440000',
    business_id: '660e8400-e29b-41d4-a716-446655440000',
    customer_id: '550e8400-e29b-41d4-a716-446655440000',
    rating: 5,
    title: 'Great Service',
    content: 'Excellent work and professional service.',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides,
  }),

  message: (overrides: Partial<any> = {}) => ({
    id: 'dd0e8400-e29b-41d4-a716-446655440000',
    thread_id: 'cc0e8400-e29b-41d4-a716-446655440000',
    sender_id: '550e8400-e29b-41d4-a716-446655440000',
    content: 'Test message content',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides,
  }),
}

// Assertion helpers
export const AssertionHelper = {
  expectApiSuccess: (response: Response, expectedStatus = 200) => {
    expect(response.status).toBe(expectedStatus)
    expect(response.ok).toBe(true)
  },

  expectApiError: (response: Response, expectedStatus = 400) => {
    expect(response.status).toBe(expectedStatus)
    expect(response.ok).toBe(false)
  },

  expectValidationError: (response: Response) => {
    expect(response.status).toBe(400)
    expect(response.ok).toBe(false)
  },

  expectUnauthorized: (response: Response) => {
    expect(response.status).toBe(401)
    expect(response.ok).toBe(false)
  },

  expectForbidden: (response: Response) => {
    expect(response.status).toBe(403)
    expect(response.ok).toBe(false)
  },

  expectNotFound: (response: Response) => {
    expect(response.status).toBe(404)
    expect(response.ok).toBe(false)
  },
}

// Performance testing helpers
export const PerformanceHelper = {
  measureExecutionTime: async <T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> => {
    const start = performance.now()
    const result = await fn()
    const end = performance.now()
    return { result, duration: end - start }
  },

  expectFastResponse: (duration: number, maxMs = 1000) => {
    expect(duration).toBeLessThan(maxMs)
  },
}

// Export everything for easy importing
export * from '@testing-library/react'
export { default as userEvent } from '@testing-library/user-event'