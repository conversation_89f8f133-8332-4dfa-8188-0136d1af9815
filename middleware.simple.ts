import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  // Simple middleware that just passes through all requests
  // Use this as a fallback if the main middleware fails
  
  const { pathname } = request.nextUrl
  
  // Only redirect admin routes to login for basic protection
  if (pathname.startsWith('/admin') || pathname.startsWith('/directory')) {
    // For now, allow access - you can add basic auth later
    return NextResponse.next()
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
