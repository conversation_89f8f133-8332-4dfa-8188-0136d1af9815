import { notFound } from 'next/navigation'
import { getBusinessBySlug, getBusinessReviews } from '@/lib/database'
import { BusinessProfile } from '@/components/business-profile'
import { Header } from '@/components/header'
import type { Metadata } from 'next'

interface BusinessPageProps {
  params: Promise<{ slug: string }>
}

export async function generateMetadata({ params }: BusinessPageProps): Promise<Metadata> {
  const { slug } = await params
  const { business } = await getBusinessBySlug(slug)

  if (!business) {
    return {
      title: 'Business Not Found - Pressure Directory',
      description: 'The requested business profile could not be found.',
    }
  }

  const location = business.location
  const locationStr = location ? `${location.city}, ${location.state}` : 'Local Area'

  return {
    title: `${business.name} - Pressure Washing in ${locationStr} | Pressure Directory`,
    description: business.description || `Professional pressure washing services by ${business.name} in ${locationStr}. Read reviews, view photos, and get quotes.`,
    openGraph: {
      title: `${business.name} - Pressure Washing Services`,
      description: business.description || `Professional pressure washing services in ${locationStr}`,
      type: 'website',
    },
  }
}

export default async function BusinessPage({ params }: BusinessPageProps) {
  const { slug } = await params
  const { business, error } = await getBusinessBySlug(slug)

  if (error || !business) {
    notFound()
  }

  // Fetch initial reviews
  const { reviews } = await getBusinessReviews(business.id, 10, 0)

  return (
    <div className="min-h-screen bg-black">
      <Header />
      <BusinessProfile business={business} initialReviews={reviews} />
    </div>
  )
}