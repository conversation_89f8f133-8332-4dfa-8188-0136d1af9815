'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { authClient, profileClient } from '@/lib/supabase-client'
import type { User, Profile, AuthResponse } from '@/lib/types'

interface UserContextType {
  user: User | null
  profile: Profile | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error?: string }>
  signUp: (email: string, password: string, fullName: string) => Promise<{ error?: string }>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<Profile>) => Promise<{ error?: string }>
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [initialized, setInitialized] = useState(false)

  // Initialize auth state and set up listener
  useEffect(() => {
    let mounted = true

    // Get initial session
    const initializeAuth = async () => {
      console.log('UserProvider: Initializing auth...')

      try {
        // Try to get session, but handle errors gracefully
        const session = await authClient.getSession()
        console.log('UserProvider: Got session result:', session ? 'Session found' : 'No session')

        if (mounted) {
          if (session?.user) {
            console.log('UserProvider: Setting user and loading profile')
            setUser(session.user as User)
            // Don't await profile loading to avoid blocking
            loadUserProfile(session.user.id).catch(error => {
              console.error('Error loading profile:', error)
            })
          } else {
            // No session - user is not authenticated
            console.log('UserProvider: No session, setting user to null')
            setUser(null)
            setProfile(null)
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
        // If auth fails, assume user is not authenticated
        if (mounted) {
          setUser(null)
          setProfile(null)
        }
      } finally {
        if (mounted) {
          console.log('UserProvider: Setting loading to false')
          setLoading(false)
        }
      }
    }

    initializeAuth()

    // Listen for auth state changes
    const { data: { subscription } } = authClient.onAuthStateChange(async (event, session) => {
      if (!mounted) return

      console.log('Auth state changed:', event, session?.user?.id)

      if (session?.user) {
        setUser(session.user as User)
        await loadUserProfile(session.user.id)
      } else {
        setUser(null)
        setProfile(null)
      }

      setLoading(false)
    })

    return () => {
      mounted = false
      subscription?.unsubscribe()
    }
  }, [])

  // Load user profile from database
  const loadUserProfile = async (userId: string) => {
    try {
      const profileData = await profileClient.getProfile(userId)
      if (profileData) {
        setProfile(profileData)
      } else {
        // Create profile if it doesn't exist
        const newProfile = await profileClient.createProfile(userId, {
          full_name: user?.user_metadata?.full_name || '',
        })
        setProfile(newProfile)
      }
    } catch (error) {
      console.error('Error loading user profile:', error)
    }
  }

  // Authentication functions
  const signIn = async (email: string, password: string): Promise<{ error?: string }> => {
    setLoading(true)
    try {
      const result = await authClient.signIn(email, password)
      if (result.error) {
        return { error: result.error }
      }
      return {}
    } catch (error) {
      return { error: 'An unexpected error occurred' }
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (email: string, password: string, fullName: string): Promise<{ error?: string }> => {
    setLoading(true)
    try {
      const result = await authClient.signUp(email, password, fullName)
      if (result.error) {
        return { error: result.error }
      }
      return {}
    } catch (error) {
      return { error: 'An unexpected error occurred' }
    } finally {
      setLoading(false)
    }
  }

  const signOut = async (): Promise<void> => {
    setLoading(true)
    try {
      await authClient.signOut()
      setUser(null)
      setProfile(null)
    } catch (error) {
      console.error('Error signing out:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateProfile = async (updates: Partial<Profile>): Promise<{ error?: string }> => {
    if (!user || !profile) {
      return { error: 'No user logged in' }
    }

    try {
      const updatedProfile = await profileClient.updateProfile(user.id, updates)
      setProfile(updatedProfile)
      return {}
    } catch (error) {
      console.error('Error updating profile:', error)
      return { error: 'Failed to update profile' }
    }
  }

  return (
    <UserContext.Provider value={{
      user,
      profile,
      loading,
      signIn,
      signUp,
      signOut,
      updateProfile
    }}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider')
  }
  return context
}
