import { createServerClient } from '@/lib/supabase'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    if (!supabase) {
      return NextResponse.json(
        { error: 'Database connection not available' },
        { status: 500 }
      )
    }

    const { error } = await supabase.auth.signOut()
    
    if (error) {
      console.error('Error signing out:', error)
      return NextResponse.json(
        { error: 'Failed to sign out' },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Unexpected error during logout:', error)
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}

// Also support GET for simple logout links
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerClient()
    
    if (supabase) {
      await supabase.auth.signOut()
    }

    // Redirect to home page after logout
    return NextResponse.redirect(new URL('/', request.url))
  } catch (error) {
    console.error('Error during logout redirect:', error)
    return NextResponse.redirect(new URL('/', request.url))
  }
}
