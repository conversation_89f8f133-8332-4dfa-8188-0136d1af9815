import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { geocodeAddress } from '@/lib/geocoding'

export async function POST(request: NextRequest) {
  try {
    if (!supabaseAdmin) {
      return NextResponse.json(
        { error: 'Database not available' },
        { status: 500 }
      )
    }

    console.log('🗺️  Starting geocoding process...')

    // Get all locations that need geocoding
    const { data: locations, error } = await supabaseAdmin
      .from('locations')
      .select('*')
      .or('latitude.is.null,longitude.is.null')

    if (error) {
      console.error('Error fetching locations:', error)
      return NextResponse.json(
        { error: 'Failed to fetch locations' },
        { status: 500 }
      )
    }

    if (!locations || locations.length === 0) {
      return NextResponse.json({
        message: 'All locations already have coordinates!',
        successCount: 0,
        errorCount: 0,
        totalProcessed: 0
      })
    }

    console.log(`📍 Found ${locations.length} locations that need geocoding`)

    let successCount = 0
    let errorCount = 0
    const results = []

    // Process first 10 locations to avoid timeout
    const locationsToProcess = locations.slice(0, 10)

    for (const location of locationsToProcess) {
      try {
        const fullAddress = `${location.street_address}, ${location.city}, ${location.state} ${location.zip_code}`
        console.log(`📍 Geocoding: ${fullAddress}`)

        const result = await geocodeAddress(fullAddress)

        if (result && result.coordinates) {
          // Update the location with coordinates
          const { error: updateError } = await supabaseAdmin
            .from('locations')
            .update({
              latitude: result.coordinates.lat,
              longitude: result.coordinates.lng
            })
            .eq('id', location.id)

          if (updateError) {
            console.error(`❌ Error updating location ${location.id}:`, updateError)
            errorCount++
            results.push({
              id: location.id,
              address: fullAddress,
              status: 'error',
              error: updateError.message
            })
          } else {
            console.log(`✅ Updated: ${result.coordinates.lat}, ${result.coordinates.lng}`)
            successCount++
            results.push({
              id: location.id,
              address: fullAddress,
              status: 'success',
              coordinates: result.coordinates
            })
          }
        } else {
          console.log(`⚠️  No coordinates found for: ${fullAddress}`)
          errorCount++
          results.push({
            id: location.id,
            address: fullAddress,
            status: 'no_coordinates'
          })
        }

        // Add small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100))

      } catch (error) {
        console.error(`❌ Error geocoding location ${location.id}:`, error)
        errorCount++
        results.push({
          id: location.id,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return NextResponse.json({
      message: 'Geocoding batch completed',
      successCount,
      errorCount,
      totalProcessed: locationsToProcess.length,
      totalRemaining: locations.length - locationsToProcess.length,
      results
    })

  } catch (error) {
    console.error('Geocoding API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
