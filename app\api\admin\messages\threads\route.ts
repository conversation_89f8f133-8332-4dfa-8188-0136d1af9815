import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { requireAdminAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    await requireAdminAuth()
    
    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database not configured' }, { status: 500 })
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    
    let query = supabaseAdmin
      .from('message_threads')
      .select(`
        *,
        business:businesses(id, name, slug),
        user:profiles(id, full_name, email),
        messages(id, content, sent_at, author_id)
      `)
      .order('updated_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (status && status !== 'all') {
      query = query.eq('status', status)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching admin message threads:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ threads: data })
  } catch (error) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    await requireAdminAuth()
    
    if (!supabaseAdmin) {
      return NextResponse.json({ error: 'Database not configured' }, { status: 500 })
    }

    const body = await request.json()
    const { threadId, status, action } = body

    if (!threadId) {
      return NextResponse.json({ error: 'Thread ID is required' }, { status: 400 })
    }

    let updateData: any = { updated_at: new Date().toISOString() }

    if (status) {
      updateData.status = status
    }

    if (action === 'flag') {
      updateData.status = 'flagged'
    } else if (action === 'unflag') {
      updateData.status = 'active'
    }

    const { data, error } = await supabaseAdmin
      .from('message_threads')
      .update(updateData)
      .eq('id', threadId)
      .select(`
        *,
        business:businesses(id, name, slug),
        user:profiles(id, full_name, email)
      `)
      .single()

    if (error) {
      console.error('Error updating thread:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ thread: data })
  } catch (error) {
    return NextResponse.json({ error: 'Authentication required' }, { status: 401 })
  }
}
