"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Droplets, User, MessageSquare, LogOut, Settings, Loader2 } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { AuthModal, LoginModal, SignUpModal } from "@/components/auth/auth-modal"
import { useUser } from "@/hooks/use-user"

export function Header() {
  const { user, profile, loading, signOut } = useUser()

  const handleSignOut = async () => {
    await signOut()
  }

  return (
    <header className="border-b border-neutral-800 bg-neutral-900 sticky top-0 z-50">
      <div className="w-full px-6 py-4 flex items-center justify-between">
        <Link href="/" className="flex items-center space-x-2">
          <div className="bg-blue-gradient p-2 rounded-xl glow-blue">
            <Droplets className="h-6 w-6 text-white" />
          </div>
          <span className="text-xl font-semibold text-white">Pressure Directory</span>
        </Link>

        <nav className="hidden md:flex items-center space-x-6">
          <Link href="/search" className="text-neutral-400 hover:text-white transition-colors">
            Find Services
          </Link>
          <Link href="/how-it-works" className="text-neutral-400 hover:text-white transition-colors">
            How It Works
          </Link>
          <Link href="/for-businesses" className="text-neutral-400 hover:text-white transition-colors">
            For Businesses
          </Link>
        </nav>

        <div className="flex items-center space-x-4">
          {loading ? (
            <Button variant="ghost" disabled className="text-neutral-400">
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Loading...
            </Button>
          ) : user ? (
            // Authenticated user menu
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="text-blue-400 hover:text-blue-300">
                  <User className="h-4 w-4 mr-2" />
                  {profile?.full_name || user.email}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-neutral-900 border-neutral-800" align="end">
                <DropdownMenuItem asChild>
                  <Link href="/dashboard" className="text-neutral-300 hover:text-white">
                    <Settings className="h-4 w-4 mr-2" />
                    Dashboard
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/messages" className="text-neutral-300 hover:text-white">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Messages
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator className="bg-neutral-800" />
                <DropdownMenuItem
                  onClick={handleSignOut}
                  className="text-red-400 hover:text-red-300 hover:bg-neutral-800 cursor-pointer"
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            // Unauthenticated user buttons
            <div className="flex items-center space-x-2">
              <LoginModal>
                <Button variant="ghost" className="text-blue-400 hover:text-blue-300 hover:bg-blue-500/10">
                  Sign In
                </Button>
              </LoginModal>
              <SignUpModal>
                <Button className="bg-blue-gradient-hover text-white shadow-lg">
                  Sign Up
                </Button>
              </SignUpModal>
            </div>
          )}
        </div>
      </div>
    </header>
  )
}
