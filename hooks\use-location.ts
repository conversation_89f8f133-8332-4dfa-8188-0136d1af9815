"use client"

import { useState, useEffect } from 'react'

interface LocationData {
  latitude: number
  longitude: number
  city?: string
  state?: string
  zipCode?: string
}

interface LocationState {
  location: LocationData | null
  loading: boolean
  error: string | null
  hasPermission: boolean
}

export function useLocation() {
  const [state, setState] = useState<LocationState>({
    location: null,
    loading: false,
    error: null,
    hasPermission: false
  })

  const requestLocation = async () => {
    if (!navigator.geolocation) {
      setState(prev => ({
        ...prev,
        error: 'Geolocation is not supported by this browser',
        loading: false
      }))
      return
    }

    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000 // 5 minutes
          }
        )
      })

      const { latitude, longitude } = position.coords

      // Try to get city/state from reverse geocoding
      let city, state, zipCode
      try {
        const response = await fetch(
          `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${latitude}&longitude=${longitude}&localityLanguage=en`
        )
        
        if (response.ok) {
          const data = await response.json()
          city = data.city || data.locality
          state = data.principalSubdivision
          zipCode = data.postcode
        }
      } catch (geocodeError) {
        console.warn('Reverse geocoding failed:', geocodeError)
        // Continue without city/state info
      }

      setState({
        location: { latitude, longitude, city, state, zipCode },
        loading: false,
        error: null,
        hasPermission: true
      })
    } catch (error) {
      let errorMessage = 'Failed to get location'

      if (error instanceof GeolocationPositionError) {
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied by user'
            // Remember that user denied location to avoid asking again
            localStorage.setItem('location-denied', 'true')
            break
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information unavailable'
            break
          case error.TIMEOUT:
            errorMessage = 'Location request timed out'
            break
        }
      }

      setState({
        location: null,
        loading: false,
        error: errorMessage,
        hasPermission: false
      })
    }
  }

  // Auto-request location on mount if not already requested
  useEffect(() => {
    const hasRequestedLocation = localStorage.getItem('location-requested')
    const locationDenied = localStorage.getItem('location-denied')

    // Auto-request location if:
    // 1. Haven't requested before OR user previously allowed
    // 2. User hasn't explicitly denied location
    // 3. Not currently loading
    // 4. Don't have location yet
    if (!locationDenied && !state.location && !state.loading) {
      // Check if geolocation is available
      if (navigator.geolocation) {
        // Small delay to ensure component is mounted
        const timer = setTimeout(() => {
          requestLocation()
        }, 300)

        return () => clearTimeout(timer)
      }
    }
  }, [])

  const clearLocation = () => {
    setState({
      location: null,
      loading: false,
      error: null,
      hasPermission: false
    })
    localStorage.removeItem('location-requested')
    localStorage.removeItem('location-denied')
  }

  const saveLocationPreference = () => {
    localStorage.setItem('location-requested', 'true')
  }

  return {
    ...state,
    requestLocation: () => {
      saveLocationPreference()
      requestLocation()
    },
    clearLocation
  }
}
