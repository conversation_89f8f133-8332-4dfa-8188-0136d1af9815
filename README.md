# Pressure Directory

A comprehensive, high-traffic pressure washing business directory built with Next.js, Supabase, and Tailwind CSS. This application follows the complete Development Guide specifications for creating a successful two-sided marketplace.

## 🚀 Quick Start

### 1. Prerequisites
- Node.js 18+ installed
- A Supabase account (free tier works for development)

### 2. Setup
```bash
# Clone and install dependencies
npm install

# Follow the detailed setup guide
# See SETUP.md for complete instructions
```

### 3. Configure Environment
Update `.env.local` with your Supabase credentials:
```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 4. Set Up Database
Run the complete schema script in your Supabase SQL Editor:
```sql
-- Copy and paste contents of scripts/03-complete-schema.sql
```

### 5. Test Integration
```bash
node scripts/test-integration.js
```

### 6. Start Development
```bash
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see your directory!

## 📋 Features

### For Homeowners
- **Location-based search** - Find pressure washing services in your area
- **Advanced filtering** - Filter by services, ratings, and more
- **Business profiles** - Detailed information, photos, and reviews
- **Quote requests** - Direct messaging with service providers
- **Review system** - Leave and read authentic customer reviews

### For Businesses
- **Free business profiles** - Claim and customize your listing
- **Service management** - Select which services you offer
- **Portfolio gallery** - Showcase your work with before/after photos
- **Lead management** - Receive and respond to quote requests
- **Review management** - View and respond to customer reviews
- **Dashboard analytics** - Track profile views and engagement

### For Platform Owners
- **SEO-optimized** - Every page built for search engine visibility
- **Content management** - Built-in blog system for content marketing
- **Multi-tenancy** - Secure data isolation between businesses
- **Scalable architecture** - Built to handle high traffic loads

## 🏗️ Architecture

### Tech Stack
- **Frontend**: Next.js 14 with App Router
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **Styling**: Tailwind CSS with custom design system
- **Deployment**: Vercel (recommended)

### Database Design
- **Multi-tenant architecture** with Row-Level Security
- **UUID primary keys** for security and scalability
- **Denormalized ratings** for performance
- **Comprehensive relationships** between all entities

### Security
- **Row-Level Security (RLS)** policies on all tables
- **JWT-based authentication** with Supabase Auth
- **Service role separation** for admin operations
- **Input validation** and sanitization

## 📖 Documentation

- **[SETUP.md](SETUP.md)** - Complete setup instructions
- **[INTEGRATION_STATUS.md](INTEGRATION_STATUS.md)** - Current implementation status
- **[Development_Guide.txt](Development_Guide.txt)** - Original comprehensive guide
- **[designrules.md](designrules.md)** - UI/UX design guidelines

## 🧪 Testing

Run the integration test to verify everything is working:
```bash
node scripts/test-integration.js
```

This will check:
- Database connection and schema
- Seed data loading
- Storage bucket configuration
- Basic functionality

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on every push

### Other Platforms
The application can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 📈 Growth Strategy

This platform implements the complete growth strategy from the Development Guide:

### Phase 1: Cold Start Solution
- Pre-populate directory with business listings
- SEO-optimized pages for organic discovery
- Clear value proposition for both sides

### Phase 2: Content Marketing
- Built-in blog system for content creation
- Local SEO optimization
- Cost guides and educational content

### Phase 3: Monetization
- Freemium model with premium subscriptions
- Pay-per-lead options
- Featured listing upgrades

## 🤝 Contributing

This project follows the specifications from the comprehensive Development Guide. When contributing:

1. Follow the established design patterns
2. Maintain the multi-tenant security model
3. Ensure all new features work with RLS policies
4. Add appropriate tests for new functionality

## 📄 License

This project is built following the Development Guide specifications for creating a production-ready pressure washing directory platform.

## 🆘 Support

If you encounter issues:

1. Check the browser console for error messages
2. Review the Supabase logs in your dashboard
3. Verify all environment variables are set correctly
4. Run the integration test script
5. Refer to the detailed setup documentation

---

**Built with ❤️ following the comprehensive Development Guide for high-traffic service directories.**